"""
Encryption utilities for the Secure Journaling App.
Handles AES encryption/decryption of journal entries using Fernet.
"""

import os
import base64
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from config import Config

class EncryptionManager:
    """Manages encryption and decryption of journal entries."""
    
    def __init__(self):
        self.fernet = None
        self.salt = None
    
    def derive_key_from_passphrase(self, passphrase: str, salt: bytes = None) -> bytes:
        """
        Derive an encryption key from a user passphrase using PBKDF2.
        
        Args:
            passphrase (str): User-provided passphrase
            salt (bytes): Salt for key derivation (generated if None)
        
        Returns:
            bytes: Derived encryption key
        """
        if salt is None:
            salt = os.urandom(Config.SALT_LENGTH)
        
        self.salt = salt
        
        # Convert passphrase to bytes
        passphrase_bytes = passphrase.encode('utf-8')
        
        # Create PBKDF2 key derivation function
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,  # 32 bytes for Fernet
            salt=salt,
            iterations=Config.KEY_ITERATIONS,
        )
        
        # Derive the key
        key = base64.urlsafe_b64encode(kdf.derive(passphrase_bytes))
        return key
    
    def initialize_encryption(self, passphrase: str, salt: bytes = None):
        """
        Initialize the encryption system with a passphrase.
        
        Args:
            passphrase (str): User-provided passphrase
            salt (bytes): Salt for key derivation (optional)
        """
        key = self.derive_key_from_passphrase(passphrase, salt)
        self.fernet = Fernet(key)
    
    def encrypt_text(self, plaintext: str) -> bytes:
        """
        Encrypt a text string.
        
        Args:
            plaintext (str): Text to encrypt
        
        Returns:
            bytes: Encrypted data
        
        Raises:
            ValueError: If encryption is not initialized
        """
        if self.fernet is None:
            raise ValueError("Encryption not initialized. Call initialize_encryption first.")
        
        plaintext_bytes = plaintext.encode('utf-8')
        encrypted_data = self.fernet.encrypt(plaintext_bytes)
        return encrypted_data
    
    def decrypt_text(self, encrypted_data: bytes) -> str:
        """
        Decrypt encrypted data back to text.
        
        Args:
            encrypted_data (bytes): Encrypted data to decrypt
        
        Returns:
            str: Decrypted text
        
        Raises:
            ValueError: If encryption is not initialized
            InvalidToken: If decryption fails (wrong key or corrupted data)
        """
        if self.fernet is None:
            raise ValueError("Encryption not initialized. Call initialize_encryption first.")
        
        decrypted_bytes = self.fernet.decrypt(encrypted_data)
        return decrypted_bytes.decode('utf-8')
    
    def get_salt(self) -> bytes:
        """
        Get the current salt used for key derivation.
        
        Returns:
            bytes: Salt used for key derivation
        """
        return self.salt
    
    def clear_session(self):
        """Clear encryption keys from memory for security."""
        self.fernet = None
        self.salt = None
