#!/usr/bin/env python3
"""
Test script for the Secure Journaling App.
Tests core functionality without GUI.
"""

import os
import sys
from database import DatabaseManager, User, JournalEntry
from auth import Auth<PERSON>anager
from encryption import Encryption<PERSON>anager

def test_core_functionality():
    """Test the core functionality of the application."""
    print("Testing Secure Journaling App Core Functionality")
    print("=" * 50)
    
    # Clean up any existing test database
    test_db = "test_journal.db"
    if os.path.exists(test_db):
        os.remove(test_db)
    
    # Override database URL for testing
    os.environ['DATABASE_URL'] = f'sqlite:///{test_db}'
    
    try:
        # Test 1: Database initialization
        print("1. Testing database initialization...")
        db_manager = DatabaseManager()
        print("   ✓ Database initialized successfully")
        
        # Test 2: Authentication manager
        print("2. Testing authentication...")
        auth_manager = AuthManager()
        test_passcode = "test123456"
        passcode_hash = auth_manager.hash_passcode(test_passcode)
        print("   ✓ Passcode hashed successfully")
        
        # Verify passcode
        is_valid = auth_manager.verify_passcode(test_passcode, passcode_hash)
        assert is_valid, "Passcode verification failed"
        print("   ✓ Passcode verification successful")
        
        # Test 3: Encryption
        print("3. Testing encryption...")
        encryption_manager = EncryptionManager()
        encryption_manager.initialize_encryption(test_passcode)
        
        test_text = "This is a secret journal entry!"
        encrypted_data = encryption_manager.encrypt_text(test_text)
        decrypted_text = encryption_manager.decrypt_text(encrypted_data)
        
        assert decrypted_text == test_text, "Encryption/decryption failed"
        print("   ✓ Encryption/decryption successful")
        
        # Test 4: User creation
        print("4. Testing user creation...")
        salt = encryption_manager.get_salt()

        # Check if user already exists
        existing_user = db_manager.get_user("test_user")
        if existing_user:
            user = existing_user
            print(f"   ✓ Using existing user with ID: {user.id}")
        else:
            user = db_manager.create_user("test_user", passcode_hash, salt)
            assert user is not None, "User creation failed"
            print(f"   ✓ User created with ID: {user.id}")
        
        # Test 5: Journal entry creation
        print("5. Testing journal entry creation...")
        entry_content = "Today was a great day! I learned about encryption and security."
        encrypted_content = encryption_manager.encrypt_text(entry_content)
        
        entry = db_manager.create_journal_entry(
            user.id,
            "My First Entry",
            encrypted_content
        )
        assert entry is not None, "Journal entry creation failed"
        print(f"   ✓ Journal entry created with ID: {entry.id}")
        
        # Test 6: Entry retrieval and decryption
        print("6. Testing entry retrieval...")
        entries = db_manager.get_journal_entries(user.id)
        assert len(entries) == 1, "Entry retrieval failed"
        
        retrieved_entry = entries[0]
        decrypted_content = encryption_manager.decrypt_text(retrieved_entry.encrypted_content)
        assert decrypted_content == entry_content, "Content decryption failed"
        print("   ✓ Entry retrieved and decrypted successfully")
        
        # Test 7: Visibility management
        print("7. Testing visibility management...")
        assert not retrieved_entry.is_visible, "Entry should be hidden by default"
        
        db_manager.update_entry_visibility(retrieved_entry.id, True)
        updated_entries = db_manager.get_journal_entries(user.id)
        assert updated_entries[0].is_visible, "Visibility update failed"
        print("   ✓ Visibility management successful")
        
        # Test 8: Authentication flow
        print("8. Testing authentication flow...")
        auth_manager.authenticate_user(test_passcode, passcode_hash)
        assert auth_manager.is_user_authenticated(), "Authentication failed"
        
        current_passphrase = auth_manager.get_current_passphrase()
        assert current_passphrase == test_passcode, "Passphrase retrieval failed"
        print("   ✓ Authentication flow successful")
        
        print("\n" + "=" * 50)
        print("✅ ALL TESTS PASSED! The application is working correctly.")
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Clean up test database
        if os.path.exists(test_db):
            os.remove(test_db)
        print(f"\n🧹 Cleaned up test database: {test_db}")

if __name__ == "__main__":
    success = test_core_functionality()
    sys.exit(0 if success else 1)
