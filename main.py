#!/usr/bin/env python3
"""
Main entry point for the Secure Journaling App.
A privacy-focused journaling application with encryption and secure authentication.
"""

import sys
import traceback
from gui import JournalingApp

def main():
    """Main function to start the application."""
    try:
        # Create and run the application
        app = JournalingApp()
        app.run()
        
    except KeyboardInterrupt:
        print("\nApplication interrupted by user.")
        sys.exit(0)
        
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        print("Traceback:")
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
