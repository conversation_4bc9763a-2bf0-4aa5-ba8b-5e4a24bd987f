"""
Database models and management for the Secure Journaling App.
Uses SQLAlchemy ORM with support for SQLite and PostgreSQL.
"""

from datetime import datetime
from sqlalchemy import create_engine, Column, Integer, String, DateTime, LargeBinary, Boolean, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from config import Config

# Create declarative base
Base = declarative_base()

class User(Base):
    """User model for storing authentication data."""
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, nullable=False, default='default_user')
    passcode_hash = Column(String(255), nullable=False)
    salt = Column(LargeBinary, nullable=False)  # Salt for encryption key derivation
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class JournalEntry(Base):
    """Journal entry model for storing encrypted entries."""
    __tablename__ = 'journal_entries'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, nullable=False, default=1)  # Foreign key to User
    title = Column(String(255), nullable=False)
    encrypted_content = Column(LargeBinary, nullable=False)  # Encrypted journal content
    is_visible = Column(Boolean, default=False)  # Visibility state
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class DatabaseManager:
    """Manages database connections and operations."""
    
    def __init__(self):
        self.engine = None
        self.SessionLocal = None
        self.initialize_database()
    
    def initialize_database(self):
        """Initialize database connection and create tables."""
        database_url = Config.get_database_url()
        
        # Create engine with appropriate settings
        if Config.is_sqlite():
            self.engine = create_engine(
                database_url,
                connect_args={"check_same_thread": False}  # For SQLite
            )
        else:
            self.engine = create_engine(database_url)
        
        # Create session factory
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
        # Create all tables
        Base.metadata.create_all(bind=self.engine)
    
    def get_session(self) -> Session:
        """Get a new database session."""
        return self.SessionLocal()
    
    def create_user(self, username: str, passcode_hash: str, salt: bytes) -> User:
        """
        Create a new user in the database.
        
        Args:
            username (str): Username
            passcode_hash (str): Hashed passcode
            salt (bytes): Salt for encryption
        
        Returns:
            User: Created user object
        """
        session = self.get_session()
        try:
            user = User(username=username, passcode_hash=passcode_hash, salt=salt)
            session.add(user)
            session.commit()
            session.refresh(user)
            return user
        finally:
            session.close()
    
    def get_user(self, username: str = 'default_user') -> User:
        """
        Get user by username.
        
        Args:
            username (str): Username to search for
        
        Returns:
            User: User object or None if not found
        """
        session = self.get_session()
        try:
            return session.query(User).filter(User.username == username).first()
        finally:
            session.close()
    
    def create_journal_entry(self, user_id: int, title: str, encrypted_content: bytes) -> JournalEntry:
        """
        Create a new journal entry.
        
        Args:
            user_id (int): User ID
            title (str): Entry title
            encrypted_content (bytes): Encrypted content
        
        Returns:
            JournalEntry: Created entry object
        """
        session = self.get_session()
        try:
            entry = JournalEntry(
                user_id=user_id,
                title=title,
                encrypted_content=encrypted_content,
                is_visible=False
            )
            session.add(entry)
            session.commit()
            session.refresh(entry)
            return entry
        finally:
            session.close()
    
    def get_journal_entries(self, user_id: int):
        """
        Get all journal entries for a user.
        
        Args:
            user_id (int): User ID
        
        Returns:
            list[JournalEntry]: List of journal entries
        """
        session = self.get_session()
        try:
            return session.query(JournalEntry).filter(JournalEntry.user_id == user_id).order_by(JournalEntry.created_at.desc()).all()
        finally:
            session.close()
    
    def update_entry_visibility(self, entry_id: int, is_visible: bool):
        """
        Update the visibility of a journal entry.
        
        Args:
            entry_id (int): Entry ID
            is_visible (bool): New visibility state
        """
        session = self.get_session()
        try:
            entry = session.query(JournalEntry).filter(JournalEntry.id == entry_id).first()
            if entry:
                entry.is_visible = is_visible
                entry.updated_at = datetime.utcnow()
                session.commit()
        finally:
            session.close()
    
    def delete_entry(self, entry_id: int):
        """
        Delete a journal entry.
        
        Args:
            entry_id (int): Entry ID to delete
        """
        session = self.get_session()
        try:
            entry = session.query(JournalEntry).filter(JournalEntry.id == entry_id).first()
            if entry:
                session.delete(entry)
                session.commit()
        finally:
            session.close()
