"""
GUI module for the Secure Journaling App.
Implements the Tkinter-based user interface.
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog, scrolledtext
from datetime import datetime
from typing import Optional
from database import DatabaseManager, JournalEntry
from auth import AuthManager
from encryption import Encry<PERSON><PERSON>anager
from config import Config

class JournalingApp:
    """Main application class for the Secure Journaling App."""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title(Config.APP_NAME)
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # Initialize managers
        self.db_manager = DatabaseManager()
        self.auth_manager = AuthManager()
        self.encryption_manager = EncryptionManager()
        
        # Application state
        self.current_user = None
        self.entries_visible = False
        
        # GUI components
        self.main_frame = None
        self.entries_listbox = None
        self.content_text = None
        self.status_label = None
        
        # Initialize the application
        self.setup_gui()
        self.authenticate_user()
    
    def setup_gui(self):
        """Set up the main GUI components."""
        # Create main frame
        self.main_frame = ttk.Frame(self.root, padding="10")
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        self.main_frame.columnconfigure(1, weight=1)
        self.main_frame.rowconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(self.main_frame, text=Config.APP_NAME, font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Left panel - Entry list
        left_frame = ttk.LabelFrame(self.main_frame, text="Journal Entries", padding="5")
        left_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        left_frame.rowconfigure(0, weight=1)
        left_frame.columnconfigure(0, weight=1)
        
        # Entries listbox with scrollbar
        listbox_frame = ttk.Frame(left_frame)
        listbox_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        listbox_frame.rowconfigure(0, weight=1)
        listbox_frame.columnconfigure(0, weight=1)
        
        self.entries_listbox = tk.Listbox(listbox_frame, width=30)
        self.entries_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.entries_listbox.bind('<<ListboxSelect>>', self.on_entry_select)
        
        # Scrollbar for listbox
        listbox_scrollbar = ttk.Scrollbar(listbox_frame, orient="vertical", command=self.entries_listbox.yview)
        listbox_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.entries_listbox.configure(yscrollcommand=listbox_scrollbar.set)
        
        # Right panel - Entry content
        right_frame = ttk.LabelFrame(self.main_frame, text="Entry Content", padding="5")
        right_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        right_frame.rowconfigure(0, weight=1)
        right_frame.columnconfigure(0, weight=1)
        
        # Content text area
        self.content_text = scrolledtext.ScrolledText(right_frame, wrap=tk.WORD, state=tk.DISABLED)
        self.content_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Button panel
        button_frame = ttk.Frame(self.main_frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=(20, 0))
        
        # Buttons
        ttk.Button(button_frame, text="Add Entry", command=self.add_entry).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Reveal Entries", command=self.reveal_entries).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Hide Entries", command=self.hide_entries).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Toggle Selected", command=self.toggle_selected_entry).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Delete Selected", command=self.delete_selected_entry).pack(side=tk.LEFT, padx=(0, 10))
        
        # Status bar
        self.status_label = ttk.Label(self.main_frame, text="Entries are hidden. Authenticate to reveal.", relief=tk.SUNKEN)
        self.status_label.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # Load entries (hidden initially)
        self.refresh_entries_list()
    
    def authenticate_user(self):
        """Handle user authentication on startup."""
        # Check if user exists
        user = self.db_manager.get_user()
        
        if user is None:
            # First time setup
            self.setup_new_user()
        else:
            # Existing user - authenticate
            self.login_user(user)
    
    def setup_new_user(self):
        """Set up a new user with passcode."""
        messagebox.showinfo("Welcome", "Welcome to the Secure Journaling App!\nPlease set up your passcode.")
        
        while True:
            passcode = simpledialog.askstring("Setup Passcode", "Enter a secure passcode:", show='*')
            if passcode is None:
                self.root.quit()
                return
            
            if len(passcode) < 6:
                messagebox.showerror("Error", "Passcode must be at least 6 characters long.")
                continue
            
            confirm_passcode = simpledialog.askstring("Confirm Passcode", "Confirm your passcode:", show='*')
            if confirm_passcode != passcode:
                messagebox.showerror("Error", "Passcodes do not match. Please try again.")
                continue
            
            # Create user
            try:
                passcode_hash = self.auth_manager.hash_passcode(passcode)
                self.encryption_manager.initialize_encryption(passcode)
                salt = self.encryption_manager.get_salt()
                
                self.current_user = self.db_manager.create_user('default_user', passcode_hash, salt)
                self.auth_manager.authenticate_user(passcode, passcode_hash)
                
                messagebox.showinfo("Success", "User setup complete! You are now logged in.")
                self.update_status("User authenticated. Entries are hidden.")
                break
                
            except Exception as e:
                messagebox.showerror("Error", f"Failed to create user: {e}")
                self.root.quit()
                return
    
    def login_user(self, user):
        """Authenticate existing user."""
        while True:
            passcode = simpledialog.askstring("Login", "Enter your passcode:", show='*')
            if passcode is None:
                self.root.quit()
                return
            
            if self.auth_manager.authenticate_user(passcode, user.passcode_hash):
                self.current_user = user
                self.encryption_manager.initialize_encryption(passcode, user.salt)
                self.update_status("User authenticated. Entries are hidden.")
                break
            else:
                messagebox.showerror("Error", "Invalid passcode. Please try again.")
    
    def refresh_entries_list(self):
        """Refresh the entries list in the GUI."""
        self.entries_listbox.delete(0, tk.END)
        
        if self.current_user is None:
            return
        
        entries = self.db_manager.get_journal_entries(self.current_user.id)
        
        for entry in entries:
            # Show entry title with visibility indicator
            visibility_indicator = "👁" if entry.is_visible else "🔒"
            display_text = f"{visibility_indicator} {entry.title} ({entry.created_at.strftime('%Y-%m-%d %H:%M')})"
            self.entries_listbox.insert(tk.END, display_text)
            
            # Store entry ID as data
            self.entries_listbox.insert(tk.END, entry.id)
            self.entries_listbox.delete(tk.END)  # Remove the ID line, but keep it in memory
    
    def on_entry_select(self, event):
        """Handle entry selection in the listbox."""
        selection = self.entries_listbox.curselection()
        if not selection:
            return
        
        # Get the selected entry
        entries = self.db_manager.get_journal_entries(self.current_user.id)
        if selection[0] < len(entries):
            entry = entries[selection[0]]
            self.display_entry_content(entry)
    
    def display_entry_content(self, entry: JournalEntry):
        """Display the content of a journal entry."""
        self.content_text.config(state=tk.NORMAL)
        self.content_text.delete(1.0, tk.END)
        
        if entry.is_visible and self.entries_visible:
            try:
                # Decrypt and display content
                decrypted_content = self.encryption_manager.decrypt_text(entry.encrypted_content)
                self.content_text.insert(1.0, decrypted_content)
            except Exception as e:
                self.content_text.insert(1.0, f"Error decrypting content: {e}")
        else:
            self.content_text.insert(1.0, "[Entry is hidden. Reveal entries to view content.]")
        
        self.content_text.config(state=tk.DISABLED)

    def add_entry(self):
        """Add a new journal entry."""
        if not self.auth_manager.is_user_authenticated():
            messagebox.showerror("Error", "You must be authenticated to add entries.")
            return

        # Get entry title
        title = simpledialog.askstring("New Entry", "Enter entry title:")
        if not title:
            return

        # Create entry dialog
        entry_dialog = EntryDialog(self.root, "New Journal Entry")
        content = entry_dialog.get_content()

        if content:
            try:
                # Encrypt content
                encrypted_content = self.encryption_manager.encrypt_text(content)

                # Save to database
                self.db_manager.create_journal_entry(
                    self.current_user.id,
                    title,
                    encrypted_content
                )

                self.refresh_entries_list()
                self.update_status(f"Entry '{title}' added successfully.")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to add entry: {e}")

    def reveal_entries(self):
        """Reveal all journal entries after passcode verification."""
        if not self.verify_passcode_for_action():
            return

        self.entries_visible = True

        # Set all entries as visible
        entries = self.db_manager.get_journal_entries(self.current_user.id)
        for entry in entries:
            self.db_manager.update_entry_visibility(entry.id, True)

        self.refresh_entries_list()
        self.update_status("All entries revealed.")

    def hide_entries(self):
        """Hide all journal entries."""
        self.entries_visible = False

        # Set all entries as hidden
        entries = self.db_manager.get_journal_entries(self.current_user.id)
        for entry in entries:
            self.db_manager.update_entry_visibility(entry.id, False)

        self.refresh_entries_list()
        self.clear_content_display()
        self.update_status("All entries hidden.")

    def toggle_selected_entry(self):
        """Toggle visibility of the selected entry."""
        selection = self.entries_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select an entry to toggle.")
            return

        entries = self.db_manager.get_journal_entries(self.current_user.id)
        if selection[0] < len(entries):
            entry = entries[selection[0]]
            new_visibility = not entry.is_visible

            if new_visibility and not self.verify_passcode_for_action():
                return

            self.db_manager.update_entry_visibility(entry.id, new_visibility)
            self.refresh_entries_list()

            action = "revealed" if new_visibility else "hidden"
            self.update_status(f"Entry '{entry.title}' {action}.")

    def delete_selected_entry(self):
        """Delete the selected entry after confirmation."""
        selection = self.entries_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select an entry to delete.")
            return

        entries = self.db_manager.get_journal_entries(self.current_user.id)
        if selection[0] < len(entries):
            entry = entries[selection[0]]

            # Confirm deletion
            if messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete '{entry.title}'?\nThis action cannot be undone."):
                self.db_manager.delete_entry(entry.id)
                self.refresh_entries_list()
                self.clear_content_display()
                self.update_status(f"Entry '{entry.title}' deleted.")

    def verify_passcode_for_action(self) -> bool:
        """Verify passcode for sensitive actions."""
        passcode = simpledialog.askstring("Verify Passcode", "Enter your passcode to continue:", show='*')
        if passcode is None:
            return False

        return self.auth_manager.verify_passcode(passcode, self.current_user.passcode_hash)

    def clear_content_display(self):
        """Clear the content display area."""
        self.content_text.config(state=tk.NORMAL)
        self.content_text.delete(1.0, tk.END)
        self.content_text.config(state=tk.DISABLED)

    def update_status(self, message: str):
        """Update the status bar message."""
        self.status_label.config(text=message)

    def run(self):
        """Start the application main loop."""
        self.root.mainloop()

class EntryDialog:
    """Dialog for creating/editing journal entries."""

    def __init__(self, parent, title="Journal Entry"):
        self.result = None

        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("600x400")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center the dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
        self.dialog.geometry(f"600x400+{x}+{y}")

        self.setup_dialog()

    def setup_dialog(self):
        """Set up the dialog components."""
        # Main frame
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Instructions
        ttk.Label(main_frame, text="Enter your journal entry content:").pack(anchor=tk.W, pady=(0, 10))

        # Text area
        self.text_area = scrolledtext.ScrolledText(main_frame, wrap=tk.WORD)
        self.text_area.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        self.text_area.focus()

        # Button frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        # Buttons
        ttk.Button(button_frame, text="Save", command=self.save_entry).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(button_frame, text="Cancel", command=self.cancel_entry).pack(side=tk.RIGHT)

    def save_entry(self):
        """Save the entry content."""
        content = self.text_area.get(1.0, tk.END).strip()
        if content:
            self.result = content
            self.dialog.destroy()
        else:
            messagebox.showwarning("Warning", "Entry content cannot be empty.")

    def cancel_entry(self):
        """Cancel entry creation."""
        self.dialog.destroy()

    def get_content(self) -> Optional[str]:
        """Get the entry content after dialog closes."""
        self.dialog.wait_window()
        return self.result
