"""
Configuration management for the Secure Journaling App.
Handles environment variables and application settings.
"""

import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    """Application configuration class."""
    
    # Database configuration
    DATABASE_URL = os.getenv('DATABASE_URL', 'sqlite:///journal.db')
    
    # Application settings
    APP_NAME = os.getenv('APP_NAME', 'Secure Journaling App')
    DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'
    
    # Security settings
    SALT_LENGTH = 32  # Length of salt for key derivation
    KEY_ITERATIONS = 100000  # PBKDF2 iterations
    
    @classmethod
    def get_database_url(cls):
        """Get the database URL for SQLAlchemy."""
        return cls.DATABASE_URL
    
    @classmethod
    def is_sqlite(cls):
        """Check if using SQLite database."""
        return cls.DATABASE_URL.startswith('sqlite')
    
    @classmethod
    def is_postgresql(cls):
        """Check if using PostgreSQL database."""
        return cls.DATABASE_URL.startswith('postgresql')
