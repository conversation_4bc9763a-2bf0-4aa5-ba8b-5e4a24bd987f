# Secure Journaling App - Usage Guide

## 🎉 Application Successfully Created!

I have successfully created a complete secure journaling application based on your requirements. Here's what has been built:

## 📁 Files Created

### Core Application Files:
- **`main.py`** - Application entry point
- **`config.py`** - Configuration management with environment variables
- **`database.py`** - SQLAlchemy models and database operations
- **`encryption.py`** - AES encryption using Fernet with PBKDF2 key derivation
- **`auth.py`** - Argon2 password hashing and authentication
- **`gui.py`** - Complete Tkinter GUI interface

### Configuration Files:
- **`requirements.txt`** - Python dependencies
- **`.env`** - Environment variables (database configuration)
- **`README.md`** - Comprehensive documentation
- **`USAGE_GUIDE.md`** - This usage guide

### Test Files:
- **`test_app.py`** - Comprehensive test suite
- **`verify_app.py`** - Simple verification script

## 🔐 Security Features Implemented

✅ **AES Encryption**: All journal entries encrypted with <PERSON><PERSON><PERSON> before storage
✅ **PBKDF2 Key Derivation**: Encryption keys derived from user passphrase (100,000 iterations)
✅ **Argon2 Password Hashing**: Secure passcode storage
✅ **Entry Visibility Control**: Hide/reveal individual or all entries
✅ **Session Security**: Keys cleared from memory when not needed
✅ **No Plaintext Storage**: Journal content never stored unencrypted

## 🚀 How to Run the Application

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Start the Application
```bash
python main.py
```

### 3. First-Time Setup
- The app will prompt you to create a secure passcode (minimum 6 characters)
- Confirm your passcode
- You're now ready to use the application!

## 🖥️ Using the GUI

### Main Interface Features:
- **Left Panel**: List of journal entries with visibility indicators
  - 🔒 = Hidden entry
  - 👁 = Visible entry
- **Right Panel**: Entry content display
- **Buttons**:
  - **Add Entry**: Create a new journal entry
  - **Reveal Entries**: Show all hidden entries (requires passcode)
  - **Hide Entries**: Hide all entries
  - **Toggle Selected**: Show/hide individual selected entry
  - **Delete Selected**: Permanently delete selected entry

### Workflow:
1. **Adding Entries**: Click "Add Entry", enter title and content
2. **Viewing Entries**: Click "Reveal Entries" and enter passcode
3. **Managing Visibility**: Use "Toggle Selected" for individual entries
4. **Security**: Entries are hidden by default on startup

## 🗄️ Database Configuration

### Default (SQLite):
```
DATABASE_URL=sqlite:///journal.db
```

### PostgreSQL (Production):
```
DATABASE_URL=postgresql://username:password@localhost:5432/journal_db
```

Edit the `.env` file to change database settings.

## 🔧 Technical Architecture

### Modules:
- **Config**: Environment variable management
- **Database**: SQLAlchemy ORM with User and JournalEntry models
- **Encryption**: Fernet encryption with PBKDF2 key derivation
- **Auth**: Argon2 password hashing and session management
- **GUI**: Tkinter interface with entry management

### Security Parameters:
- Salt length: 32 bytes
- PBKDF2 iterations: 100,000
- AES-256 encryption via Fernet
- Argon2id password hashing

## 🛡️ Security Best Practices

1. **Strong Passcode**: Use a unique, strong passcode
2. **Regular Backups**: Backup your `journal.db` file
3. **Secure Environment**: Keep `.env` file secure
4. **Updates**: Keep dependencies updated

## 🔍 Verification

The application has been tested and verified to work correctly. All core functionality including:
- Database operations
- Encryption/decryption
- Authentication
- GUI components

## 📝 Notes

- **Database File**: `journal.db` is created in the application directory
- **Lost Passcode**: Cannot be recovered due to security design
- **Cross-Platform**: Works on Windows, macOS, and Linux
- **Dependencies**: All required packages are listed in `requirements.txt`

## 🎯 Application Meets All Requirements

✅ **SQLite/PostgreSQL Support**: Configurable via environment variables
✅ **AES Encryption**: Fernet implementation with secure key derivation
✅ **Argon2 Authentication**: Secure passcode hashing
✅ **Entry Visibility**: Hide/reveal functionality implemented
✅ **Tkinter GUI**: Clean, intuitive interface
✅ **Environment Variables**: `.env` file configuration
✅ **Modular Code**: Well-organized, documented modules
✅ **Security First**: No sensitive data logging, secure session management

The application is ready for use and fully implements all the security requirements specified in your Requirements.md file!
