#!/usr/bin/env python3
"""
Test script to verify that entry saving works correctly.
"""

import os
import tempfile
from database import DatabaseManager
from auth import Auth<PERSON>anager
from encryption import EncryptionMana<PERSON>

def test_entry_saving():
    """Test that entries can be saved and retrieved correctly."""
    print("🧪 Testing Entry Saving Functionality")
    print("=" * 40)
    
    # Use a temporary database for testing
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
        temp_db_path = tmp_file.name
    
    # Set temporary database URL
    original_db_url = os.environ.get('DATABASE_URL')
    os.environ['DATABASE_URL'] = f'sqlite:///{temp_db_path}'
    
    try:
        # Initialize components
        db_manager = DatabaseManager()
        auth_manager = AuthManager()
        encryption_manager = EncryptionManager()
        
        # Set up user
        test_passcode = "test123456"
        passcode_hash = auth_manager.hash_passcode(test_passcode)
        encryption_manager.initialize_encryption(test_passcode)
        salt = encryption_manager.get_salt()
        
        user = db_manager.create_user("test_user", passcode_hash, salt)
        print(f"✅ User created with ID: {user.id}")
        
        # Test 1: Create and save an entry
        print("\n1. Testing entry creation and saving...")
        entry_title = "My Test Entry"
        entry_content = "This is a test journal entry to verify saving functionality."
        
        encrypted_content = encryption_manager.encrypt_text(entry_content)
        new_entry = db_manager.create_journal_entry(user.id, entry_title, encrypted_content)
        
        print(f"   ✅ Entry created with ID: {new_entry.id}")
        print(f"   ✅ Entry title: {new_entry.title}")
        print(f"   ✅ Entry is hidden by default: {not new_entry.is_visible}")
        
        # Test 2: Retrieve and verify the entry
        print("\n2. Testing entry retrieval...")
        entries = db_manager.get_journal_entries(user.id)
        
        assert len(entries) == 1, f"Expected 1 entry, found {len(entries)}"
        retrieved_entry = entries[0]
        
        print(f"   ✅ Retrieved entry ID: {retrieved_entry.id}")
        print(f"   ✅ Retrieved entry title: {retrieved_entry.title}")
        
        # Test 3: Decrypt and verify content
        print("\n3. Testing content decryption...")
        decrypted_content = encryption_manager.decrypt_text(retrieved_entry.encrypted_content)
        
        assert decrypted_content == entry_content, "Content mismatch after decryption"
        print(f"   ✅ Content matches: '{decrypted_content[:50]}...'")
        
        # Test 4: Test visibility management
        print("\n4. Testing visibility management...")
        
        # Make entry visible
        db_manager.update_entry_visibility(retrieved_entry.id, True)
        updated_entries = db_manager.get_journal_entries(user.id)
        
        assert updated_entries[0].is_visible, "Entry should be visible after update"
        print("   ✅ Entry visibility updated successfully")
        
        # Test 5: Create multiple entries
        print("\n5. Testing multiple entries...")
        
        for i in range(3):
            title = f"Entry {i+2}"
            content = f"This is entry number {i+2} for testing multiple entries."
            encrypted = encryption_manager.encrypt_text(content)
            db_manager.create_journal_entry(user.id, title, encrypted)
        
        all_entries = db_manager.get_journal_entries(user.id)
        assert len(all_entries) == 4, f"Expected 4 entries, found {len(all_entries)}"
        print(f"   ✅ Created {len(all_entries)} entries total")
        
        # Test 6: Verify all entries can be decrypted
        print("\n6. Testing all entries decryption...")
        
        for i, entry in enumerate(all_entries):
            decrypted = encryption_manager.decrypt_text(entry.encrypted_content)
            print(f"   ✅ Entry {i+1}: '{entry.title}' - Content length: {len(decrypted)} chars")
        
        print("\n" + "=" * 40)
        print("🎉 ALL SAVING TESTS PASSED!")
        print("✅ Entries can be created and saved successfully")
        print("✅ Entries can be retrieved from database")
        print("✅ Content is properly encrypted and decrypted")
        print("✅ Visibility management works correctly")
        print("✅ Multiple entries are supported")
        print("=" * 40)
        
        return True
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Restore original database URL
        if original_db_url:
            os.environ['DATABASE_URL'] = original_db_url
        else:
            os.environ.pop('DATABASE_URL', None)
        
        # Clean up temporary database
        try:
            os.unlink(temp_db_path)
        except:
            pass

if __name__ == "__main__":
    success = test_entry_saving()
    if success:
        print("\n🚀 The saving functionality is working correctly!")
        print("You can now create and save entries in the GUI application.")
    else:
        print("\n❌ There are issues with the saving functionality.")
