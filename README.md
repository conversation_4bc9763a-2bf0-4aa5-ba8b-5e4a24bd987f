# Secure Journaling App

A privacy-focused journaling application built with Python that prioritizes user security and data protection.

## Features

- **End-to-End Encryption**: All journal entries are encrypted using AES encryption (Fernet) before storage
- **Secure Authentication**: User passcodes are hashed using Argon2 for maximum security
- **Key Derivation**: Encryption keys are derived from user passphrases using PBKDF2
- **Entry Visibility Control**: Hide/reveal individual entries or all entries at once
- **Database Flexibility**: Supports both SQLite (local) and PostgreSQL (production)
- **Clean GUI**: Simple and intuitive Tkinter-based interface
- **Privacy First**: No logging of sensitive data, secure session management

## Security Features

1. **AES Encryption**: Journal content is encrypted using the `cryptography` library's Fernet implementation
2. **PBKDF2 Key Derivation**: Encryption keys are derived from user passphrases with 100,000 iterations
3. **Argon2 Password Hashing**: User passcodes are securely hashed using Argon2
4. **Session Security**: Encryption keys are cleared from memory when not needed
5. **No Plaintext Storage**: Journal content is never stored in plaintext

## Installation

1. **Clone or download the application files**

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure environment** (optional):
   - Edit `.env` file to change database settings
   - Default uses SQLite for local storage

## Usage

1. **Start the application**:
   ```bash
   python main.py
   ```

2. **First-time setup**:
   - Set up a secure passcode (minimum 6 characters)
   - Confirm your passcode

3. **Using the application**:
   - **Add Entry**: Click "Add Entry" to create a new journal entry
   - **Reveal Entries**: Click "Reveal Entries" to show all hidden entries (requires passcode)
   - **Hide Entries**: Click "Hide Entries" to hide all entries
   - **Toggle Selected**: Select an entry and click "Toggle Selected" to show/hide individual entries
   - **Delete Selected**: Select an entry and click "Delete Selected" to permanently remove it

## File Structure

```
├── main.py           # Application entry point
├── config.py         # Configuration management
├── database.py       # Database models and operations
├── encryption.py     # Encryption utilities
├── auth.py          # Authentication management
├── gui.py           # Tkinter GUI interface
├── requirements.txt  # Python dependencies
├── .env             # Environment variables
└── README.md        # This file
```

## Configuration

### Database Configuration

Edit the `.env` file to configure your database:

**SQLite (Default)**:
```
DATABASE_URL=sqlite:///journal.db
```

**PostgreSQL**:
```
DATABASE_URL=postgresql://username:password@localhost:5432/journal_db
```

### Security Settings

The application uses the following security parameters (configurable in `config.py`):
- Salt length: 32 bytes
- PBKDF2 iterations: 100,000
- AES encryption via Fernet

## Dependencies

- **SQLAlchemy**: Database ORM
- **cryptography**: AES encryption (Fernet)
- **argon2-cffi**: Password hashing
- **python-dotenv**: Environment variable management
- **psycopg2-binary**: PostgreSQL support

## Security Considerations

1. **Passcode Strength**: Use a strong, unique passcode
2. **Backup**: Regularly backup your database file
3. **Environment**: Keep your `.env` file secure
4. **Updates**: Keep dependencies updated for security patches

## Troubleshooting

### Common Issues

1. **Database Connection Error**: Check your `DATABASE_URL` in `.env`
2. **Permission Denied**: Ensure write permissions for SQLite database file
3. **Import Errors**: Verify all dependencies are installed via `pip install -r requirements.txt`

### Data Recovery

- SQLite database is stored as `journal.db` in the application directory
- Entries are encrypted and require the original passcode to decrypt
- Lost passcodes cannot be recovered due to security design

## License

This application is provided as-is for educational and personal use.

## Contributing

This is a standalone security-focused application. Ensure any modifications maintain the security principles and encryption standards.
