
# Secure Journaling App - AI Development Prompt

Develop a secure journaling application using Python. The app must prioritize user privacy and follow modern security best practices throughout its architecture and implementation. Below are the precise development requirements:

## Core Features:

### 1. Database Setup:
- Implement support for both SQLite (local use) and PostgreSQL (production use).
- Use SQLAlchemy as the ORM to abstract database interactions and allow seamless switching between the two.
- Default to SQLite for local development and provide PostgreSQL setup in a configuration file for production deployment.

### 2. Security & Privacy:
- Encrypt all journal entries before storing them in the database using AES encryption (Fernet from the `cryptography` package).
- Derive the encryption key from a user-supplied passphrase using PBKDF2 for added security.
- Store the derived key securely during the session and do not log or persist sensitive data at any time.

### 3. User Authentication:
- Require a passcode on application startup.
- Store only a secure hash of the passcode using Argon2.
- Authenticate the user at startup and whenever protected actions are triggered (e.g., viewing entries).

### 4. Entry Visibility Management:
- Journal entries are hidden by default when the app starts.
- Reveal entries only after the user verifies their passcode.
- Allow the user to hide or reveal individual entries through the interface by selecting and toggling their visibility.

### 5. Interface:
- Use a simple GUI built with T<PERSON><PERSON>.
- The interface must support the following:
  - Add a new journal entry.
  - View journal entries (after passcode verification).
  - Hide/reveal selected entries.
- Ensure all password inputs are masked, and the UI is clean and intuitive.

## Additional Requirements:
- Use a `.env` file for managing environment variables and sensitive settings like database URLs.
- Organize the codebase into modules for database handling, encryption, authentication, and the user interface.
- Write clean, well-documented, and maintainable code.

This application must follow strict security principles and provide a smooth journaling experience with a minimal and secure user interface.
