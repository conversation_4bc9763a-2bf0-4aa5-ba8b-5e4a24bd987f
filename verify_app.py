#!/usr/bin/env python3
"""
Simple verification script for the Secure Journaling App.
Tests core functionality without GUI interaction.
"""

import os
import tempfile
from database import DatabaseManager
from auth import AuthManager
from encryption import EncryptionManager

def verify_app():
    """Verify the core functionality works."""
    print("🔍 Verifying Secure Journaling App...")
    
    # Use a temporary database for testing
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
        temp_db_path = tmp_file.name
    
    # Set temporary database URL
    original_db_url = os.environ.get('DATABASE_URL')
    os.environ['DATABASE_URL'] = f'sqlite:///{temp_db_path}'
    
    try:
        # Test 1: Basic imports and initialization
        print("✅ All modules imported successfully")
        
        # Test 2: Database initialization
        db_manager = DatabaseManager()
        print("✅ Database initialized")
        
        # Test 3: Authentication
        auth_manager = AuthManager()
        test_passcode = "secure123"
        passcode_hash = auth_manager.hash_passcode(test_passcode)
        print("✅ Authentication system working")
        
        # Test 4: Encryption
        encryption_manager = EncryptionManager()
        encryption_manager.initialize_encryption(test_passcode)
        
        test_message = "This is a secret message!"
        encrypted = encryption_manager.encrypt_text(test_message)
        decrypted = encryption_manager.decrypt_text(encrypted)
        
        assert decrypted == test_message, "Encryption/decryption failed"
        print("✅ Encryption system working")
        
        # Test 5: Database operations
        salt = encryption_manager.get_salt()
        user = db_manager.create_user("test_user", passcode_hash, salt)
        print(f"✅ User created with ID: {user.id}")
        
        # Test 6: Journal entry
        encrypted_content = encryption_manager.encrypt_text("My first journal entry!")
        entry = db_manager.create_journal_entry(user.id, "Test Entry", encrypted_content)
        print(f"✅ Journal entry created with ID: {entry.id}")
        
        # Test 7: Entry retrieval
        entries = db_manager.get_journal_entries(user.id)
        assert len(entries) == 1, "Entry retrieval failed"
        
        retrieved_content = encryption_manager.decrypt_text(entries[0].encrypted_content)
        assert retrieved_content == "My first journal entry!", "Content mismatch"
        print("✅ Entry retrieval and decryption working")
        
        print("\n🎉 ALL VERIFICATIONS PASSED!")
        print("The Secure Journaling App is ready to use.")
        print("\nTo start the application, run:")
        print("   python main.py")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Verification failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Restore original database URL
        if original_db_url:
            os.environ['DATABASE_URL'] = original_db_url
        else:
            os.environ.pop('DATABASE_URL', None)
        
        # Clean up temporary database
        try:
            os.unlink(temp_db_path)
        except:
            pass

if __name__ == "__main__":
    verify_app()
