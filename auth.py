"""
Authentication module for the Secure Journaling App.
Handles user passcode verification using Argon2 hashing.
"""

import os
from argon2 import PasswordHasher
from argon2.exceptions import VerifyMismatchError, HashingError

class AuthManager:
    """Manages user authentication and passcode verification."""
    
    def __init__(self):
        self.ph = PasswordHasher()
        self.is_authenticated = False
        self.current_passphrase = None
    
    def hash_passcode(self, passcode: str) -> str:
        """
        Hash a passcode using Argon2.
        
        Args:
            passcode (str): Plain text passcode
        
        Returns:
            str: Hashed passcode
        """
        try:
            return self.ph.hash(passcode)
        except HashingError as e:
            raise ValueError(f"Failed to hash passcode: {e}")
    
    def verify_passcode(self, passcode: str, hashed_passcode: str) -> bool:
        """
        Verify a passcode against its hash.
        
        Args:
            passcode (str): Plain text passcode to verify
            hashed_passcode (str): Stored hash to verify against
        
        Returns:
            bool: True if passcode is correct, False otherwise
        """
        try:
            self.ph.verify(hashed_passcode, passcode)
            return True
        except VerifyMismatchError:
            return False
        except Exception as e:
            print(f"Error during passcode verification: {e}")
            return False
    
    def authenticate_user(self, passcode: str, stored_hash: str) -> bool:
        """
        Authenticate a user with their passcode.
        
        Args:
            passcode (str): User-provided passcode
            stored_hash (str): Stored hash from database
        
        Returns:
            bool: True if authentication successful
        """
        if self.verify_passcode(passcode, stored_hash):
            self.is_authenticated = True
            self.current_passphrase = passcode
            return True
        return False
    
    def logout(self):
        """Clear authentication state and sensitive data."""
        self.is_authenticated = False
        self.current_passphrase = None
    
    def require_authentication(self):
        """
        Check if user is authenticated.
        
        Raises:
            PermissionError: If user is not authenticated
        """
        if not self.is_authenticated:
            raise PermissionError("User must be authenticated to perform this action")
    
    def get_current_passphrase(self) -> str:
        """
        Get the current user's passphrase for encryption.
        
        Returns:
            str: Current passphrase
        
        Raises:
            PermissionError: If user is not authenticated
        """
        self.require_authentication()
        return self.current_passphrase
    
    def is_user_authenticated(self) -> bool:
        """
        Check if user is currently authenticated.
        
        Returns:
            bool: True if authenticated, False otherwise
        """
        return self.is_authenticated
